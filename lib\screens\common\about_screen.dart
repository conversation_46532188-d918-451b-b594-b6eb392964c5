import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../widgets/common/app_logo.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حول التطبيق'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildAppHeader(),
            const SizedBox(height: AppConstants.largePadding),
            _buildAppInfo(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildFeatures(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildDeveloperInfo(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildLegalInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primaryBlue, AppColors.primaryBlueLight],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const AppLogo(
            type: LogoType.icon,
            width: 100,
            height: 100,
            color: AppColors.textWhite,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          const Text(
            AppConstants.appName,
            style: TextStyle(
              color: AppColors.textWhite,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'نظام الحضور والانصراف',
            style: TextStyle(color: AppColors.textWhite, fontSize: 16),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.textWhite.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'الإصدار ${AppConstants.appVersion}',
              style: const TextStyle(
                color: AppColors.textWhite,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات التطبيق',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            const Text(
              'تطبيق ClockIn هو نظام شامل لإدارة الحضور والانصراف يساعد الشركات والمؤسسات على تتبع حضور الموظفين بدقة وفعالية. يوفر التطبيق واجهات سهلة الاستخدام للموظفين والإدارة مع إمكانيات متقدمة لتتبع الموقع والتقارير التفصيلية.',
              style: TextStyle(
                fontSize: 16,
                height: 1.6,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatures() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المميزات الرئيسية',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildFeatureItem(
              Icons.access_time,
              'تسجيل الحضور والانصراف',
              'تسجيل سهل وسريع للحضور والانصراف مع تتبع الوقت',
            ),
            _buildFeatureItem(
              Icons.location_on,
              'تتبع الموقع بـ GPS',
              'تحديد موقع الموظف عند تسجيل الحضور لضمان الدقة',
            ),
            _buildFeatureItem(
              Icons.assessment,
              'التقارير التفصيلية',
              'تقارير شاملة عن الحضور والأداء والإحصائيات',
            ),
            _buildFeatureItem(
              Icons.notifications,
              'الإشعارات الذكية',
              'تنبيهات وتذكيرات للموظفين والإدارة',
            ),
            _buildFeatureItem(
              Icons.security,
              'الأمان والخصوصية',
              'حماية عالية للبيانات مع تشفير متقدم',
            ),
            _buildFeatureItem(
              Icons.mobile_friendly,
              'واجهة سهلة الاستخدام',
              'تصميم عصري وسهل الاستخدام لجميع المستخدمين',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primaryBlueLight,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: AppColors.primaryBlue, size: 20),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeveloperInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات المطور',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ListTile(
              leading: const Icon(Icons.business, color: AppColors.primaryBlue),
              title: const Text('اسم الشركة'),
              subtitle: const Text('شركة التقنيات المتقدمة'),
            ),
            ListTile(
              leading: const Icon(Icons.email, color: AppColors.primaryBlue),
              title: const Text('البريد الإلكتروني'),
              subtitle: const Text('<EMAIL>'),
            ),
            ListTile(
              leading: const Icon(Icons.phone, color: AppColors.primaryBlue),
              title: const Text('رقم الهاتف'),
              subtitle: const Text('+966 11 123 4567'),
            ),
            ListTile(
              leading: const Icon(Icons.web, color: AppColors.primaryBlue),
              title: const Text('الموقع الإلكتروني'),
              subtitle: const Text('www.clockin.com'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegalInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات القانونية',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ListTile(
              leading: const Icon(
                Icons.copyright,
                color: AppColors.primaryBlue,
              ),
              title: const Text('حقوق الطبع والنشر'),
              subtitle: const Text(
                '© 2024 شركة التقنيات المتقدمة. جميع الحقوق محفوظة.',
              ),
            ),
            ListTile(
              leading: const Icon(
                Icons.privacy_tip,
                color: AppColors.primaryBlue,
              ),
              title: const Text('سياسة الخصوصية'),
              subtitle: const Text('اطلع على سياسة الخصوصية الخاصة بنا'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Navigate to privacy policy
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.description,
                color: AppColors.primaryBlue,
              ),
              title: const Text('شروط الاستخدام'),
              subtitle: const Text('اطلع على شروط وأحكام الاستخدام'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Navigate to terms of service
              },
            ),
            ListTile(
              leading: const Icon(Icons.security, color: AppColors.primaryBlue),
              title: const Text('الأمان والحماية'),
              subtitle: const Text('معلومات حول أمان البيانات والحماية'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Navigate to security info
              },
            ),
          ],
        ),
      ),
    );
  }
}
