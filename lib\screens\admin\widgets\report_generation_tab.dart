import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../providers/providers.dart';
import '../../../models/models.dart';

class ReportGenerationTab extends StatefulWidget {
  const ReportGenerationTab({super.key});

  @override
  State<ReportGenerationTab> createState() => _ReportGenerationTabState();
}

class _ReportGenerationTabState extends State<ReportGenerationTab> {
  final _formKey = GlobalKey<FormState>();
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  int? _selectedEmployeeId;
  int? _selectedSiteId;
  bool _includeStats = true;
  String _reportType = 'all_employees';
  String _selectedFormat = 'csv'; // Format par défaut

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildReportTypeSelector(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildDateRangeSelector(),
            const SizedBox(height: AppConstants.defaultPadding),
            if (_reportType == 'individual') _buildEmployeeSelector(),
            if (_reportType == 'site') _buildSiteSelector(),
            if (_reportType == 'individual' || _reportType == 'site')
              const SizedBox(height: AppConstants.defaultPadding),
            _buildOptionsSection(),
            const SizedBox(height: AppConstants.defaultPadding * 2),
            _buildGenerateButton(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildRecentReports(),
          ],
        ),
      ),
    );
  }

  Widget _buildReportTypeSelector() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نوع التقرير',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Column(
              children: [
                RadioListTile<String>(
                  title: const Text('تقرير جميع الموظفين'),
                  subtitle: const Text('تقرير شامل لجميع الموظفين'),
                  value: 'all_employees',
                  groupValue: _reportType,
                  onChanged: (value) {
                    setState(() {
                      _reportType = value!;
                      _selectedEmployeeId = null;
                      _selectedSiteId = null;
                    });
                  },
                  activeColor: AppColors.primaryBlue,
                ),
                RadioListTile<String>(
                  title: const Text('تقرير موظف محدد'),
                  subtitle: const Text('تقرير مفصل لموظف واحد'),
                  value: 'individual',
                  groupValue: _reportType,
                  onChanged: (value) {
                    setState(() {
                      _reportType = value!;
                      _selectedSiteId = null;
                    });
                  },
                  activeColor: AppColors.primaryBlue,
                ),
                RadioListTile<String>(
                  title: const Text('تقرير موقع محدد'),
                  subtitle: const Text('تقرير لجميع موظفي موقع معين'),
                  value: 'site',
                  groupValue: _reportType,
                  onChanged: (value) {
                    setState(() {
                      _reportType = value!;
                      _selectedEmployeeId = null;
                    });
                  },
                  activeColor: AppColors.primaryBlue,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeSelector() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'فترة التقرير',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectStartDate(context),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.borderLight),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            color: AppColors.primaryBlue,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'من تاريخ',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              Text(
                                DateFormat('dd/MM/yyyy').format(_startDate),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectEndDate(context),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.borderLight),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            color: AppColors.primaryBlue,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'إلى تاريخ',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              Text(
                                DateFormat('dd/MM/yyyy').format(_endDate),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                _buildQuickDateButton('آخر 7 أيام', 7),
                const SizedBox(width: 8),
                _buildQuickDateButton('آخر 30 يوم', 30),
                const SizedBox(width: 8),
                _buildQuickDateButton('آخر 90 يوم', 90),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickDateButton(String label, int days) {
    return Expanded(
      child: OutlinedButton(
        onPressed: () {
          setState(() {
            _endDate = DateTime.now();
            _startDate = _endDate.subtract(Duration(days: days));
          });
        },
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primaryBlue,
          side: const BorderSide(color: AppColors.primaryBlue),
          padding: const EdgeInsets.symmetric(vertical: 8),
        ),
        child: Text(label, style: const TextStyle(fontSize: 12)),
      ),
    );
  }

  Widget _buildEmployeeSelector() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار الموظف',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Consumer<EmployeesProvider>(
              builder: (context, employeesProvider, child) {
                if (employeesProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                return DropdownButtonFormField<int>(
                  value: _selectedEmployeeId,
                  decoration: const InputDecoration(
                    labelText: 'اختر الموظف',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person),
                  ),
                  items: employeesProvider.employees.map((employee) {
                    return DropdownMenuItem<int>(
                      value: employee.id,
                      child: Text(employee.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedEmployeeId = value;
                    });
                  },
                  validator: (value) {
                    if (_reportType == 'individual' && value == null) {
                      return 'يرجى اختيار موظف';
                    }
                    return null;
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSiteSelector() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار الموقع',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Consumer<SitesProvider>(
              builder: (context, sitesProvider, child) {
                if (sitesProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                return DropdownButtonFormField<int>(
                  value: _selectedSiteId,
                  decoration: const InputDecoration(
                    labelText: 'اختر الموقع',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.location_on),
                  ),
                  items: sitesProvider.sites.map((site) {
                    return DropdownMenuItem<int>(
                      value: site.id,
                      child: Text(site.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedSiteId = value;
                    });
                  },
                  validator: (value) {
                    if (_reportType == 'site' && value == null) {
                      return 'يرجى اختيار موقع';
                    }
                    return null;
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'خيارات التقرير',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            SwitchListTile(
              title: const Text('تضمين الإحصائيات التفصيلية'),
              subtitle: const Text('إضافة جداول وإحصائيات مفصلة للتقرير'),
              value: _includeStats,
              onChanged: (value) {
                setState(() {
                  _includeStats = value;
                });
              },
              activeColor: AppColors.primaryBlue,
            ),
            const Divider(),
            Text(
              'تنسيق الملف',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('CSV'),
                    subtitle: const Text('ملف نصي (موصى به)'),
                    value: 'csv',
                    groupValue: _selectedFormat,
                    onChanged: (value) {
                      setState(() {
                        _selectedFormat = value ?? 'csv';
                      });
                    },
                    activeColor: AppColors.primaryBlue,
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('Excel'),
                    subtitle: const Text('ملف Excel (تجريبي)'),
                    value: 'excel',
                    groupValue: _selectedFormat,
                    onChanged: (value) {
                      setState(() {
                        _selectedFormat = value ?? 'csv';
                      });
                    },
                    activeColor: AppColors.primaryBlue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenerateButton() {
    return Consumer<ReportsProvider>(
      builder: (context, reportsProvider, child) {
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: reportsProvider.isLoading ? null : _generateReport,
            icon: reportsProvider.isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.assessment),
            label: Text(
              reportsProvider.isLoading
                  ? 'جاري إنشاء التقرير...'
                  : 'إنشاء التقرير',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: AppColors.textWhite,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentReports() {
    return Consumer<ReportsProvider>(
      builder: (context, reportsProvider, child) {
        if (!reportsProvider.hasReports) {
          return const SizedBox.shrink();
        }

        return Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'التقارير المُنشأة مؤخراً',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryBlue,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: reportsProvider.generatedReports.take(3).length,
                  separatorBuilder: (context, index) => const Divider(),
                  itemBuilder: (context, index) {
                    final report = reportsProvider.generatedReports[index];
                    return ListTile(
                      leading: const CircleAvatar(
                        backgroundColor: AppColors.primaryBlueLight,
                        child: Icon(
                          Icons.description,
                          color: AppColors.primaryBlue,
                        ),
                      ),
                      title: Text(
                        report.filename,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('الحجم: ${report.fileSize}'),
                          Text(
                            'تاريخ الإنشاء: ${_formatDateTime(report.generatedAt)}',
                          ),
                        ],
                      ),
                      trailing: IconButton(
                        icon: const Icon(
                          Icons.download,
                          color: AppColors.primaryBlue,
                        ),
                        onPressed: () => _downloadReport(report.filename),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );
    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
        if (_startDate.isAfter(_endDate)) {
          _endDate = _startDate;
        }
      });
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate,
      firstDate: _startDate,
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );
    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  Future<void> _generateReport() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final reportsProvider = context.read<ReportsProvider>();
    reportsProvider.setDateRange(_startDate, _endDate);
    reportsProvider.setIncludeStats(_includeStats);

    bool success = false;

    try {
      switch (_reportType) {
        case 'all_employees':
          success = await reportsProvider.generateEmployeeReport();
          break;
        case 'individual':
          if (_selectedEmployeeId != null) {
            success = await reportsProvider.generateIndividualReport(
              _selectedEmployeeId!,
            );
          }
          break;
        case 'site':
          if (_selectedSiteId != null) {
            success = await reportsProvider.generateSiteReport(
              _selectedSiteId!,
            );
          }
          break;
      }

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء التقرير بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إنشاء التقرير: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _downloadReport(String filename) async {
    final reportsProvider = context.read<ReportsProvider>();
    final result = await reportsProvider.downloadReport(filename);
    final success = result?.success ?? false;

    if (mounted) {
      if (success && result != null) {
        if (result.isExcelFile) {
          // Fichier Excel téléchargé, sauvegardé et ouvert automatiquement
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حفظ وفتح ملف Excel بنجاح'),
              backgroundColor: AppColors.success,
              duration: Duration(seconds: 2),
            ),
          );

          // Afficher immédiatement les options de partage Excel
          _showExcelShareOptions(context, result);
        } else {
          // Fichier CSV téléchargé
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تحميل التقرير بنجاح'),
              backgroundColor: AppColors.success,
              action: SnackBarAction(
                label: 'عرض المحتوى',
                onPressed: () {
                  _showReportContent(context, result.filename, result.content);
                },
              ),
            ),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل التقرير'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// Afficher les options de partage pour un fichier Excel
  void _showExcelShareOptions(BuildContext context, DownloadResult result) {
    if (result.filePath == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.table_chart, color: AppColors.primaryBlue),
            SizedBox(width: 8),
            Text('ملف Excel جاهز'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تم حفظ وفتح الملف بنجاح:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              result.filename,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.success.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: AppColors.success,
                        size: 16,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'الملف محفوظ في التحميلات',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  SizedBox(height: 4),
                  Text(
                    'يمكنك الآن مشاركة الملف أو العثور عليه في مجلد التحميلات',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'مشاركة الملف:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          TextButton.icon(
            onPressed: () async {
              Navigator.of(context).pop();
              final reportsProvider = context.read<ReportsProvider>();
              await reportsProvider.shareViaEmail(
                result.filePath!,
                result.filename,
              );
            },
            icon: Icon(Icons.email, size: 16),
            label: const Text('إيميل'),
          ),
          TextButton.icon(
            onPressed: () async {
              Navigator.of(context).pop();
              final reportsProvider = context.read<ReportsProvider>();
              await reportsProvider.shareViaWhatsApp(
                result.filePath!,
                result.filename,
              );
            },
            icon: Icon(Icons.chat, size: 16, color: Colors.green),
            label: const Text('واتساب'),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              Navigator.of(context).pop();
              final reportsProvider = context.read<ReportsProvider>();
              await reportsProvider.shareExcelFile(
                result.filePath!,
                result.filename,
              );
            },
            icon: Icon(Icons.share, size: 16),
            label: const Text('مشاركة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
    } catch (e) {
      return dateTimeString;
    }
  }

  void _showReportContent(
    BuildContext context,
    String filename,
    String content,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'محتوى التقرير: $filename',
          style: const TextStyle(fontSize: 16),
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: SelectableText(
              content,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
